import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, CreditCard, Users, DollarSign } from "lucide-react";

interface PaymentConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  orderInfo: {
    orderId: string;
    customerName: string;
    totalAmount: number;
    orderCount?: number;
  };
  isProcessing?: boolean;
}

export function PaymentConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  orderInfo,
  isProcessing = false
}: PaymentConfirmationDialogProps) {
  const [isConfirming, setIsConfirming] = useState(false);

  const formatPrice = (price: number) => `¥${price.toLocaleString()}`;

  const handleConfirm = async () => {
    try {
      setIsConfirming(true);
      await onConfirm();
      onClose();
    } catch (error) {
      console.error("Payment confirmation error:", error);
    } finally {
      setIsConfirming(false);
    }
  };

  const handleClose = () => {
    if (!isConfirming && !isProcessing) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <CreditCard className="h-5 w-5 text-green-600" />
            Xác nhận thanh toán
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn đánh dấu đơn hàng này là đã thanh toán?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Customer Info */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">{orderInfo.customerName}</p>
              <p className="text-sm text-gray-500">Khách hàng</p>
            </div>
          </div>

          {/* Order Info */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">ID đơn hàng:</span>
              <Badge variant="outline" className="font-mono text-xs">
                {orderInfo.orderId.substring(0, 8)}...
              </Badge>
            </div>

            {orderInfo.orderCount && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Số đơn hàng:</span>
                <Badge variant="secondary">
                  {orderInfo.orderCount} đơn
                </Badge>
              </div>
            )}

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">Tổng tiền:</span>
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="text-lg font-bold text-green-700">
                  {formatPrice(orderInfo.totalAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* Warning */}
          <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-sm text-amber-800">
              <strong>Lưu ý:</strong> Thao tác này không thể hoàn tác. Hãy chắc chắn rằng khách hàng đã thanh toán đầy đủ.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isConfirming || isProcessing}
          >
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isConfirming || isProcessing}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isConfirming ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Đang xử lý...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Xác nhận thanh toán
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
