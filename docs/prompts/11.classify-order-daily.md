Implement comprehensive order management functionality in the OrderManagementPage component located at `src/pages/OrderManagementPage.tsx`.

** Required:**
1. Currently, show all data got from orders table in database , but i want to show as today as default, and add a calendar show pick history data(pick by day). 
and currently, order combined, but just combine per each day.

**UI/UX Implementation Requirements:**
1. **Payment Status Visual Indicators**:
   - Show today order as defaut
   - Add new calender button to pick show data of history order
   - Add a new button to sort orders.is_paid order.
**Technical Implementation Specifications:**

2. **State Management & Data Flow**:

3. **Database Integration**:
   - uderstand about this schema and data store at orders table.

4. **Code Quality & Consistency Standards**:
   - Follow camelCase naming convention for all variables, functions, and component props
   - Create reusable components and small hook, utils to split code smaller
   - Maintain consistency with existing codebase patterns, especially component structure and styling approaches
   - Add proper TypeScript types for payment-related data structures

**Database Schema Reference:**
- `users`: id, name, role (VARCHAR: 'admin'|'user'), created_at, updated_at, deleted_at
- `orders`: id, user_id, total_price, is_paid (BOOLEAN), order_date, created_at, updated_at, deleted_at
- `products`: id, name, price, category, created_at, updated_at, deleted_at
- `order_items`: id, order_id, product_id, quantity, created_at, updated_at, deleted_at
- `order_history`: id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at
- `debts`: id, user_id, amount, created_at, updated_at, deleted_at

**Testing & Validation:**
- Use Supabase CLI to verify database schema changes and test data operations
- Test role-based access control with both admin and regular user accounts
- Verify payment status updates persist correctly in database
- Test error scenarios (network failures, unauthorized access, invalid data)
- Run "npm run lint" to check typescript lint of project.

** Coding rules
- use small methods
